import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/active_user_model.dart';
import '../services/active_users_service.dart';

// حالة المستخدمين النشطين
class ActiveUsersState {
  final List<ActiveUserModel> users;
  final ActiveUserModel? currentUser;
  final ActiveUserModel? selectedUser;
  final List<UserNotification> notifications;
  final Map<String, dynamic> globalStats;
  final bool isLoading;
  final String? error;
  final bool showOnlineOnly;
  final String searchQuery;
  final String selectedCountry;

  const ActiveUsersState({
    this.users = const [],
    this.currentUser,
    this.selectedUser,
    this.notifications = const [],
    this.globalStats = const {},
    this.isLoading = false,
    this.error,
    this.showOnlineOnly = false,
    this.searchQuery = '',
    this.selectedCountry = '',
  });

  ActiveUsersState copyWith({
    List<ActiveUserModel>? users,
    ActiveUserModel? currentUser,
    ActiveUserModel? selectedUser,
    List<UserNotification>? notifications,
    Map<String, dynamic>? globalStats,
    bool? isLoading,
    String? error,
    bool? showOnlineOnly,
    String? searchQuery,
    String? selectedCountry,
  }) {
    return ActiveUsersState(
      users: users ?? this.users,
      currentUser: currentUser ?? this.currentUser,
      selectedUser: selectedUser ?? this.selectedUser,
      notifications: notifications ?? this.notifications,
      globalStats: globalStats ?? this.globalStats,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      showOnlineOnly: showOnlineOnly ?? this.showOnlineOnly,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCountry: selectedCountry ?? this.selectedCountry,
    );
  }

  // المستخدمون المفلترون
  List<ActiveUserModel> get filteredUsers {
    var filtered = users.where((user) {
      // فلترة حسب الاتصال
      if (showOnlineOnly && !user.isOnline) return false;
      
      // فلترة حسب البحث
      if (searchQuery.isNotEmpty) {
        final query = searchQuery.toLowerCase();
        if (!user.name.toLowerCase().contains(query) &&
            !user.city.toLowerCase().contains(query) &&
            !user.country.toLowerCase().contains(query)) {
          return false;
        }
      }
      
      // فلترة حسب البلد
      if (selectedCountry.isNotEmpty && user.country != selectedCountry) {
        return false;
      }
      
      return true;
    }).toList();
    
    // ترتيب حسب الحالة (النشطين أولاً)
    filtered.sort((a, b) {
      if (a.isActive && !b.isActive) return -1;
      if (!a.isActive && b.isActive) return 1;
      if (a.isOnline && !b.isOnline) return -1;
      if (!a.isOnline && b.isOnline) return 1;
      return a.name.compareTo(b.name);
    });
    
    return filtered;
  }

  // البلدان المتاحة
  List<String> get availableCountries {
    final countries = users.map((u) => u.country).toSet().toList();
    countries.sort();
    return countries;
  }

  // الإشعارات غير المقروءة
  List<UserNotification> get unreadNotifications {
    return notifications.where((n) => !n.isRead).toList();
  }

  // عدد الإشعارات غير المقروءة
  int get unreadCount => unreadNotifications.length;
}

// مزود المستخدمين النشطين
class ActiveUsersNotifier extends StateNotifier<ActiveUsersState> {
  final ActiveUsersService _service = ActiveUsersService();
  StreamSubscription<List<ActiveUserModel>>? _usersSubscription;
  StreamSubscription<UserNotification>? _notificationsSubscription;

  ActiveUsersNotifier() : super(const ActiveUsersState()) {
    _initializeService();
  }

  @override
  void dispose() {
    _usersSubscription?.cancel();
    _notificationsSubscription?.cancel();
    _service.stopService();
    super.dispose();
  }

  // تهيئة الخدمة
  Future<void> _initializeService() async {
    state = state.copyWith(isLoading: true);

    try {
      // بدء الخدمة وانتظار التهيئة
      await _service.startService();

      // الحصول على المستخدم الحالي
      final currentUser = await _service.getCurrentUser();

      // الاستماع للمستخدمين
      _usersSubscription = _service.usersStream.listen(
        (users) {
          print('📊 تم استلام ${users.length} مستخدم في المزود');
          if (users.isNotEmpty) {
            print('👤 المستخدم الأول: ${users.first.name}');
          }

          final stats = _service.getGlobalStats();
          state = state.copyWith(
            users: users,
            globalStats: stats,
            isLoading: false,
          );

          print('✅ تم تحديث حالة المزود - المستخدمون: ${users.length}');
        },
        onError: (error) {
          print('❌ خطأ في stream المستخدمين: $error');
          state = state.copyWith(
            error: error.toString(),
            isLoading: false,
          );
        },
      );

      // إضافة تأخير صغير للتأكد من وصول البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      // محاولة الحصول على البيانات مباشرة إذا لم تصل عبر Stream
      final currentUsers = _service.activeUsers;
      if (currentUsers.isNotEmpty && state.users.isEmpty) {
        print('🔄 إضافة المستخدمين مباشرة: ${currentUsers.length}');
        final stats = _service.getGlobalStats();
        state = state.copyWith(
          users: currentUsers,
          globalStats: stats,
          isLoading: false,
        );
      }

      // الاستماع للإشعارات
      _notificationsSubscription = _service.notificationsStream.listen(
        (notification) {
          final updatedNotifications = [notification, ...state.notifications];
          // الاحتفاظ بآخر 50 إشعار فقط
          final limitedNotifications = updatedNotifications.take(50).toList();
          
          state = state.copyWith(
            notifications: limitedNotifications,
          );
        },
      );

      state = state.copyWith(
        currentUser: currentUser,
        isLoading: false,
      );

    } catch (error) {
      state = state.copyWith(
        error: error.toString(),
        isLoading: false,
      );
    }
  }

  // اختيار مستخدم
  void selectUser(ActiveUserModel? user) {
    state = state.copyWith(selectedUser: user);
  }

  // تبديل عرض المتصلين فقط
  void toggleOnlineOnly() {
    state = state.copyWith(showOnlineOnly: !state.showOnlineOnly);
  }

  // تحديث البحث
  void updateSearch(String query) {
    state = state.copyWith(searchQuery: query);
  }

  // تحديث البلد المحدد
  void updateSelectedCountry(String country) {
    state = state.copyWith(selectedCountry: country);
  }

  // مسح الفلاتر
  void clearFilters() {
    state = state.copyWith(
      showOnlineOnly: false,
      searchQuery: '',
      selectedCountry: '',
    );
  }

  // تحديد إشعار كمقروء
  void markNotificationAsRead(String notificationId) {
    final updatedNotifications = state.notifications.map((n) {
      if (n.id == notificationId) {
        return UserNotification(
          id: n.id,
          title: n.title,
          message: n.message,
          type: n.type,
          timestamp: n.timestamp,
          isRead: true,
          data: n.data,
        );
      }
      return n;
    }).toList();

    state = state.copyWith(notifications: updatedNotifications);
  }

  // تحديد جميع الإشعارات كمقروءة
  void markAllNotificationsAsRead() {
    final updatedNotifications = state.notifications.map((n) {
      return UserNotification(
        id: n.id,
        title: n.title,
        message: n.message,
        type: n.type,
        timestamp: n.timestamp,
        isRead: true,
        data: n.data,
      );
    }).toList();

    state = state.copyWith(notifications: updatedNotifications);
  }

  // حذف إشعار
  void deleteNotification(String notificationId) {
    final updatedNotifications = state.notifications
        .where((n) => n.id != notificationId)
        .toList();

    state = state.copyWith(notifications: updatedNotifications);
  }

  // مسح جميع الإشعارات
  void clearAllNotifications() {
    state = state.copyWith(notifications: []);
  }

  // تحديث حالة المستخدم الحالي
  void updateCurrentUserStatus(UserStatus status) {
    if (state.currentUser != null) {
      final updatedUser = state.currentUser!.copyWith(
        status: status,
        lastSeen: DateTime.now(),
      );
      state = state.copyWith(currentUser: updatedUser);
    }
  }

  // تحديث مستوى خصوصية المستخدم الحالي
  void updateCurrentUserPrivacy(PrivacyLevel privacy) {
    if (state.currentUser != null) {
      final updatedUser = state.currentUser!.copyWith(privacyLevel: privacy);
      state = state.copyWith(currentUser: updatedUser);
    }
  }

  // إعادة تحميل البيانات
  void refresh() {
    _initializeService();
  }

  // مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(error: null);
  }

  // الحصول على مستخدم بالمعرف
  ActiveUserModel? getUserById(String id) {
    try {
      return state.users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  // الحصول على المستخدمين حسب البلد
  Map<String, List<ActiveUserModel>> getUsersByCountry() {
    final Map<String, List<ActiveUserModel>> result = {};
    
    for (final user in state.filteredUsers) {
      if (!result.containsKey(user.country)) {
        result[user.country] = [];
      }
      result[user.country]!.add(user);
    }
    
    return result;
  }

  // الحصول على المستخدمين النشطين
  List<ActiveUserModel> get activeUsers {
    return state.users.where((user) => user.isActive).toList();
  }

  // الحصول على المستخدمين المتصلين
  List<ActiveUserModel> get onlineUsers {
    return state.users.where((user) => user.isOnline).toList();
  }

  // الحصول على المستخدمين غير المتصلين
  List<ActiveUserModel> get offlineUsers {
    return state.users.where((user) => !user.isOnline).toList();
  }
}

// مزود المستخدمين النشطين
final activeUsersProvider = StateNotifierProvider<ActiveUsersNotifier, ActiveUsersState>((ref) {
  return ActiveUsersNotifier();
});

// مزودات مساعدة
final filteredUsersProvider = Provider<List<ActiveUserModel>>((ref) {
  final state = ref.watch(activeUsersProvider);
  return state.filteredUsers;
});

final selectedUserProvider = Provider<ActiveUserModel?>((ref) {
  final state = ref.watch(activeUsersProvider);
  return state.selectedUser;
});

final currentUserProvider = Provider<ActiveUserModel?>((ref) {
  final state = ref.watch(activeUsersProvider);
  return state.currentUser;
});

final unreadNotificationsProvider = Provider<List<UserNotification>>((ref) {
  final state = ref.watch(activeUsersProvider);
  return state.unreadNotifications;
});

final globalStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final state = ref.watch(activeUsersProvider);
  return state.globalStats;
});

final usersByCountryProvider = Provider<Map<String, List<ActiveUserModel>>>((ref) {
  final notifier = ref.watch(activeUsersProvider.notifier);
  return notifier.getUsersByCountry();
});
