import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/media_provider.dart';
import '../../models/photo_model.dart';
import '../../models/video_model.dart';

class MediaStatsBar extends ConsumerWidget {
  const MediaStatsBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaState = ref.watch(mediaProvider);
    final totalPhotosCount = ref.watch(totalPhotosCountProvider);
    final totalVideosCount = ref.watch(totalVideosCountProvider);
    final totalMediaCount = ref.watch(totalMediaCountProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        children: [
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'إجمالي الوسائط',
              value: totalMediaCount.toString(),
              icon: Icons.perm_media,
              color: AppColors.primaryGold,
              onTap: () => _showAllMediaDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'الصور',
              value: totalPhotosCount.toString(),
              icon: Icons.photo,
              color: AppColors.success,
              onTap: () => _showPhotosDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'الفيديوهات',
              value: totalVideosCount.toString(),
              icon: Icons.videocam,
              color: AppColors.info,
              onTap: () => _showVideosDialog(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildClickableStatCard(
              context,
              ref,
              title: 'حجم التخزين',
              value: _formatFileSize(_calculateTotalSize(mediaState)),
              icon: Icons.storage,
              color: AppColors.warning,
              onTap: () => _showStorageDialog(context, ref),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClickableStatCard(
    BuildContext context,
    WidgetRef ref, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.visibility,
                    color: AppColors.secondaryText,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.secondaryText,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _calculateTotalSize(MediaState mediaState) {
    int totalSize = 0;

    for (final photo in mediaState.photos) {
      totalSize += photo.fileSizeBytes ?? 0;
    }

    for (final video in mediaState.videos) {
      totalSize += video.fileSizeBytes ?? 0;
    }

    return totalSize;
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // دوال عرض النوافذ المنبثقة للإحصائيات
  void _showAllMediaDialog(BuildContext context, WidgetRef ref) {
    final mediaState = ref.read(mediaProvider);
    final allMedia = <dynamic>[...mediaState.photos, ...mediaState.videos];

    _showMediaDialog(
      context: context,
      title: 'جميع الوسائط',
      items: allMedia,
      color: AppColors.primaryGold,
      icon: Icons.perm_media,
    );
  }

  void _showPhotosDialog(BuildContext context, WidgetRef ref) {
    final mediaState = ref.read(mediaProvider);
    final photos = mediaState.photos;

    _showMediaDialog(
      context: context,
      title: 'الصور',
      items: photos,
      color: AppColors.success,
      icon: Icons.photo,
    );
  }

  void _showVideosDialog(BuildContext context, WidgetRef ref) {
    final mediaState = ref.read(mediaProvider);
    final videos = mediaState.videos;

    _showMediaDialog(
      context: context,
      title: 'الفيديوهات',
      items: videos,
      color: AppColors.info,
      icon: Icons.videocam,
    );
  }

  void _showStorageDialog(BuildContext context, WidgetRef ref) {
    final mediaState = ref.read(mediaProvider);
    final photosSize = mediaState.photos.fold<int>(0, (sum, photo) => sum + (photo.fileSizeBytes ?? 0));
    final videosSize = mediaState.videos.fold<int>(0, (sum, video) => sum + (video.fileSizeBytes ?? 0));
    final totalSize = photosSize + videosSize;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 500,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.storage, color: AppColors.warning, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'إحصائيات التخزين',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.warning,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _formatFileSize(totalSize),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      color: AppColors.secondaryText,
                    ),
                  ],
                ),
              ),
              
              // محتوى النافذة
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    _buildStorageItem(
                      'الصور',
                      photosSize,
                      totalSize,
                      AppColors.success,
                      Icons.photo,
                      mediaState.photos.length,
                    ),
                    const SizedBox(height: 16),
                    _buildStorageItem(
                      'الفيديوهات',
                      videosSize,
                      totalSize,
                      AppColors.info,
                      Icons.videocam,
                      mediaState.videos.length,
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.surfaceBackground,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppColors.warning.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info,
                            color: AppColors.warning,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'إجمالي المساحة المستخدمة: ${_formatFileSize(totalSize)}',
                              style: TextStyle(
                                color: AppColors.primaryText,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              // أسفل النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceBackground,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStorageItem(
    String title,
    int size,
    int totalSize,
    Color color,
    IconData icon,
    int count,
  ) {
    final percentage = totalSize > 0 ? (size / totalSize * 100) : 0.0;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: AppColors.primaryText,
                      ),
                    ),
                    Text(
                      '$count عنصر',
                      style: TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _formatFileSize(size),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: color,
                    ),
                  ),
                  Text(
                    '${percentage.toStringAsFixed(1)}%',
                    style: TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: color.withValues(alpha: 0.1),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  void _showMediaDialog({
    required BuildContext context,
    required String title,
    required List<dynamic> items,
    required Color color,
    required IconData icon,
  }) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 700,
          constraints: const BoxConstraints(maxHeight: 700),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(icon, color: color, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${items.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      color: AppColors.secondaryText,
                    ),
                  ],
                ),
              ),

              // محتوى النافذة
              Flexible(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: items.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                icon,
                                size: 64,
                                color: AppColors.secondaryText,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا توجد عناصر في هذه القائمة',
                                style: TextStyle(
                                  color: AppColors.secondaryText,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : GridView.builder(
                          shrinkWrap: true,
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            childAspectRatio: 1,
                          ),
                          itemCount: items.length > 20 ? 20 : items.length, // عرض أول 20 عنصر فقط
                          itemBuilder: (context, index) {
                            final item = items[index];
                            return _buildMediaThumbnail(item, color);
                          },
                        ),
                ),
              ),

              // أسفل النافذة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceBackground,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (items.length > 20)
                      Text(
                        'عرض أول 20 عنصر من أصل ${items.length}',
                        style: TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 12,
                        ),
                      )
                    else
                      const SizedBox(),
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إغلاق'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMediaThumbnail(dynamic item, Color color) {
    final isPhoto = item is PhotoModel;
    final fileName = isPhoto ? item.fileName : (item as VideoModel).fileName;
    final createdAt = isPhoto ? item.createdAt : item.createdAt;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Icon(
                isPhoto ? Icons.photo : Icons.videocam,
                color: color,
                size: 32,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            child: Column(
              children: [
                Text(
                  fileName ?? 'غير محدد',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primaryText,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (createdAt != null)
                  Text(
                    _formatDate(createdAt),
                    style: TextStyle(
                      fontSize: 8,
                      color: AppColors.secondaryText,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }
}
