import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_colors.dart';
import '../../models/user_model.dart';

class UserDetailsDialog extends StatefulWidget {
  final UserModel? user;

  const UserDetailsDialog({super.key, this.user});

  @override
  State<UserDetailsDialog> createState() => _UserDetailsDialogState();
}

class _UserDetailsDialogState extends State<UserDetailsDialog> {
  // Controllers للنموذج
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _phoneController = TextEditingController();
  final _departmentController = TextEditingController();
  final _positionController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // متغيرات الحالة
  bool _isAdmin = false;
  bool _isActive = true;
  String _accountType = 'standard';
  int _maxDevices = 3;
  int _storageQuotaMb = 1024;
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _nationalIdController.dispose();
    _phoneController.dispose();
    _departmentController.dispose();
    _positionController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.user != null;

    return Dialog(
      child: Container(
        width: 600,
        height: 700,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  isEditing ? Icons.person : Icons.person_add,
                  color: AppColors.primaryGold,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  isEditing ? 'تفاصيل المستخدم' : 'إضافة مستخدم جديد',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.primaryGold,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // محتوى النافذة
            Expanded(
              child: isEditing 
                  ? _buildUserDetails(context)
                  : _buildAddUserForm(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserDetails(BuildContext context) {
    if (widget.user == null) return const SizedBox();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم والمعلومات الأساسية
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AppColors.primaryGold,
                radius: 40,
                child: Text(
                  widget.user!.fullName?.substring(0, 1) ?? 'م',
                  style: const TextStyle(
                    color: AppColors.darkBackground,
                    fontWeight: FontWeight.bold,
                    fontSize: 24,
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.user!.fullName ?? 'غير محدد',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: AppColors.primaryText,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.user!.email ?? 'غير محدد',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.secondaryText,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildStatusChip(
                          widget.user!.isActive ? 'نشط' : 'غير نشط',
                          widget.user!.isActive ? AppColors.success : AppColors.error,
                        ),
                        const SizedBox(width: 8),
                        _buildStatusChip(
                          widget.user!.isAdmin ? 'مدير' : 'مستخدم',
                          widget.user!.isAdmin ? AppColors.primaryGold : AppColors.info,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // المعلومات التفصيلية
          _buildInfoSection(
            context,
            title: 'المعلومات الشخصية',
            icon: Icons.person,
            children: [
              _buildInfoRow('الاسم الكامل', widget.user!.fullName),
              _buildInfoRow('الرقم الوطني', widget.user!.nationalId),
              _buildInfoRow('البريد الإلكتروني', widget.user!.email),
              _buildInfoRow('رقم الهاتف', widget.user!.phone),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildInfoSection(
            context,
            title: 'معلومات العمل',
            icon: Icons.work,
            children: [
              _buildInfoRow('القسم', widget.user!.department),
              _buildInfoRow('المنصب', widget.user!.position),
              _buildInfoRow('نوع الحساب', widget.user!.accountType),
              _buildInfoRow('الحد الأقصى للأجهزة', widget.user!.maxDevices?.toString()),
              _buildInfoRow('حصة التخزين (MB)', widget.user!.storageQuotaMb?.toString()),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildInfoSection(
            context,
            title: 'معلومات النظام',
            icon: Icons.settings,
            children: [
              _buildInfoRow('تاريخ الإنشاء', _formatDateTime(widget.user!.createdAt)),
              _buildInfoRow('آخر تحديث', _formatDateTime(widget.user!.updatedAt)),
              _buildInfoRow('آخر دخول', _formatDateTime(widget.user!.lastLogin)),
              _buildInfoRow('حالة الحساب', widget.user!.isActive ? 'نشط' : 'غير نشط'),
              _buildInfoRow('صلاحيات الإدارة', widget.user!.isAdmin ? 'نعم' : 'لا'),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // أزرار الإجراءات
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    // تعديل المستخدم
                  },
                  icon: const Icon(Icons.edit),
                  label: const Text('تعديل'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // إعادة تعيين كلمة المرور
                  },
                  icon: const Icon(Icons.lock_reset),
                  label: const Text('إعادة تعيين كلمة المرور'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddUserForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // المعلومات الشخصية
            _buildFormSection(
              title: 'المعلومات الشخصية',
              icon: Icons.person,
              children: [
                _buildTextField(
                  controller: _fullNameController,
                  label: 'الاسم الكامل',
                  hint: 'أدخل الاسم الكامل',
                  icon: Icons.person_outline,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'الاسم الكامل مطلوب';
                    }
                    if (value.trim().length < 3) {
                      return 'الاسم يجب أن يكون 3 أحرف على الأقل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _emailController,
                  label: 'البريد الإلكتروني',
                  hint: '<EMAIL>',
                  icon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'البريد الإلكتروني مطلوب';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                      return 'البريد الإلكتروني غير صحيح';
                    }
                    return null;
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // أزرار الحفظ والإلغاء
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveUser,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryGold,
                      foregroundColor: AppColors.darkBackground,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('حفظ المستخدم'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // دالة حفظ المستخدم
  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // هنا يمكن إضافة منطق حفظ المستخدم في قاعدة البيانات
      await Future.delayed(const Duration(seconds: 2)); // محاكاة العملية

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء المستخدم بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.of(context).pop(true); // إرجاع true للإشارة إلى النجاح
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء المستخدم: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // دالة بناء حقل النص
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    bool obscureText = false,
    Widget? suffixIcon,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      obscureText: obscureText,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: AppColors.primaryGold),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryGold, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        filled: true,
        fillColor: AppColors.surfaceBackground,
      ),
    );
  }

  // دالة بناء قسم النموذج
  Widget _buildFormSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primaryGold, size: 22),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  color: AppColors.primaryGold,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primaryGold, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.primaryGold,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: AppColors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value ?? 'غير محدد',
              style: const TextStyle(
                color: AppColors.primaryText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'غير محدد';
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }
}
