import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:latlong2/latlong.dart';
import '../models/active_user_model.dart';
import 'user_profile_service.dart';

class ActiveUsersService {
  static final ActiveUsersService _instance = ActiveUsersService._internal();
  factory ActiveUsersService() => _instance;
  ActiveUsersService._internal();

  // Stream للمستخدمين النشطين
  final StreamController<List<ActiveUserModel>> _usersController = 
      StreamController<List<ActiveUserModel>>.broadcast();
  
  // Stream للإشعارات
  final StreamController<UserNotification> _notificationsController = 
      StreamController<UserNotification>.broadcast();

  Stream<List<ActiveUserModel>> get usersStream => _usersController.stream;
  Stream<UserNotification> get notificationsStream => _notificationsController.stream;

  List<ActiveUserModel> _activeUsers = [];
  Timer? _updateTimer;
  ActiveUserModel? _currentUser;

  // بدء الخدمة
  Future<void> startService() async {
    print('🚀 بدء خدمة تتبع المستخدمين...');
    await _initializeRealUser();
    _startPeriodicUpdates();
    print('✅ خدمة تتبع المستخدمين جاهزة');
  }

  // إيقاف الخدمة
  void stopService() {
    _updateTimer?.cancel();
    _usersController.close();
    _notificationsController.close();
    print('⏹️ خدمة تتبع المستخدمين توقفت');
  }

  // الحصول على المستخدم الحالي
  Future<ActiveUserModel?> getCurrentUser() async {
    if (_currentUser != null) return _currentUser;

    try {
      // الحصول على الموقع الحالي
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // الحصول على العنوان
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      final placemark = placemarks.first;

      // الحصول على بيانات المستخدم من قاعدة البيانات
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getCurrentUserProfile();
      final displayName = userProfile != null
          ? userProfileService.getDisplayName(userProfile)
          : 'مستخدم';

      _currentUser = ActiveUserModel(
        id: 'current_user_real',
        name: displayName,
        email: userProfile?['email'] ?? '<EMAIL>',
        location: LatLng(position.latitude, position.longitude),
        address: '${placemark.locality}, ${placemark.country}',
        country: placemark.country ?? 'غير محدد',
        city: placemark.locality ?? 'غير محدد',
        lastSeen: DateTime.now(),
        isOnline: true,
        deviceType: 'Desktop',
        deviceModel: 'Windows PC',
        batteryLevel: 100,
        status: UserStatus.working,
        privacyLevel: PrivacyLevel.public,
        stats: UserStats(
          photosToday: 25,
          videosToday: 8,
          totalPhotos: 1250,
          totalVideos: 340,
          hoursActiveToday: 6.5,
          totalHoursActive: 450.0,
          sessionsToday: 3,
          totalSessions: 89,
          joinDate: DateTime.now().subtract(const Duration(days: 120)),
        ),
      );

      // لا نضيف المستخدم هنا - سيتم إضافته في _initializeRealUser

      print('📍 تم إضافة موقعك الحقيقي: ${position.latitude}, ${position.longitude}');
      print('🏠 العنوان: ${_currentUser!.address}');

      return _currentUser;
    } catch (e) {
      print('❌ خطأ في الحصول على المستخدم الحالي: $e');

      // إنشاء مستخدم افتراضي في صنعاء إذا فشل الحصول على الموقع
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getCurrentUserProfile();
      final displayName = userProfile != null
          ? userProfileService.getDisplayName(userProfile)
          : 'مستخدم';

      _currentUser = ActiveUserModel(
        id: 'current_user_real',
        name: displayName,
        email: userProfile?['email'] ?? '<EMAIL>',
        location: const LatLng(15.3694, 44.1910), // صنعاء، اليمن
        address: 'صنعاء، اليمن',
        country: 'اليمن',
        city: 'صنعاء',
        lastSeen: DateTime.now(),
        isOnline: true,
        deviceType: 'Desktop',
        deviceModel: 'Windows PC',
        batteryLevel: 100,
        status: UserStatus.working,
        privacyLevel: PrivacyLevel.public,
        stats: UserStats(
          photosToday: 25,
          videosToday: 8,
          totalPhotos: 1250,
          totalVideos: 340,
          hoursActiveToday: 6.5,
          totalHoursActive: 450.0,
          sessionsToday: 3,
          totalSessions: 89,
          joinDate: DateTime.now().subtract(const Duration(days: 120)),
        ),
      );

      // لا نضيف المستخدم هنا - سيتم إضافته في _initializeRealUser فقط

      print('📍 تم إضافة موقع افتراضي في صنعاء');

      return _currentUser;
    }
  }

  // تهيئة المستخدم الحقيقي + مستخدمين تجريبيين للاختبار
  Future<void> _initializeRealUser() async {
    print('🔄 بدء تهيئة المستخدمين...');
    _activeUsers.clear();

    try {
      // الحصول على المستخدم الحقيقي
      final currentUser = await getCurrentUser();

      if (currentUser != null) {
        // إضافة المستخدم الحقيقي فقط
        _activeUsers.add(currentUser);

        // إرسال التحديث فوراً
        _usersController.add(List.from(_activeUsers));

        // إرسال إشعار
        _sendNotification(
          'مرحباً بك',
          'تم تسجيل دخولك من ${currentUser.city}. يوجد ${_activeUsers.length} مستخدم متصل',
          'user_login',
        );

        print('✅ تم تهيئة ${_activeUsers.length} مستخدم: ${currentUser.name} من ${currentUser.city}');
        print('📊 عدد المستخدمين النشطين: ${_activeUsers.length}');
      } else {
        print('❌ فشل في تهيئة المستخدم الحقيقي');
        // إرسال قائمة فارغة
        _usersController.add([]);
      }
    } catch (e) {
      print('❌ خطأ في تهيئة المستخدم: $e');
      _usersController.add([]);
    }
  }



  // تحديثات دورية للمستخدم الحقيقي فقط
  void _startPeriodicUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateRealUserStatus();
    });
  }

  // تحديث حالة المستخدم الحقيقي
  void _updateRealUserStatus() {
    if (_currentUser != null && _activeUsers.isNotEmpty) {
      // تحديث آخر ظهور للمستخدم الحقيقي
      final updatedUser = _currentUser!.copyWith(
        lastSeen: DateTime.now(),
        isOnline: true,
        batteryLevel: 100, // افتراض أن الكمبيوتر متصل بالكهرباء
      );

      _currentUser = updatedUser;
      _activeUsers[0] = updatedUser;
      _usersController.add(_activeUsers);

      print('🔄 تم تحديث حالة المستخدم الحقيقي');
    }
  }



  // إرسال إشعار
  void _sendNotification(String title, String message, String type, {Map<String, dynamic>? data}) {
    final notification = UserNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );
    
    _notificationsController.add(notification);
  }

  // الحصول على المستخدمين النشطين
  List<ActiveUserModel> get activeUsers => List.unmodifiable(_activeUsers);

  // الحصول على المستخدمين حسب البلد
  Map<String, List<ActiveUserModel>> getUsersByCountry() {
    final Map<String, List<ActiveUserModel>> result = {};
    
    for (final user in _activeUsers) {
      if (!result.containsKey(user.country)) {
        result[user.country] = [];
      }
      result[user.country]!.add(user);
    }
    
    return result;
  }

  // الحصول على إحصائيات عامة
  Map<String, dynamic> getGlobalStats() {
    final totalUsers = _activeUsers.length;
    final onlineUsers = _activeUsers.where((u) => u.isOnline).length;
    final activeUsers = _activeUsers.where((u) => u.isActive).length;
    final totalPhotosToday = _activeUsers.fold<int>(0, (sum, u) => sum + u.stats.photosToday);
    final totalVideosToday = _activeUsers.fold<int>(0, (sum, u) => sum + u.stats.videosToday);
    
    return {
      'totalUsers': totalUsers,
      'onlineUsers': onlineUsers,
      'activeUsers': activeUsers,
      'offlineUsers': totalUsers - onlineUsers,
      'totalPhotosToday': totalPhotosToday,
      'totalVideosToday': totalVideosToday,
      'totalMediaToday': totalPhotosToday + totalVideosToday,
      'countries': getUsersByCountry().length,
    };
  }
}
