import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:latlong2/latlong.dart';
import '../models/active_user_model.dart';
import 'user_profile_service.dart';
import 'admin_supabase_service.dart';

class ActiveUsersService {
  static final ActiveUsersService _instance = ActiveUsersService._internal();
  factory ActiveUsersService() => _instance;
  ActiveUsersService._internal();

  // Stream للمستخدمين النشطين
  final StreamController<List<ActiveUserModel>> _usersController = 
      StreamController<List<ActiveUserModel>>.broadcast();
  
  // Stream للإشعارات
  final StreamController<UserNotification> _notificationsController = 
      StreamController<UserNotification>.broadcast();

  Stream<List<ActiveUserModel>> get usersStream => _usersController.stream;
  Stream<UserNotification> get notificationsStream => _notificationsController.stream;

  List<ActiveUserModel> _activeUsers = [];
  Timer? _updateTimer;
  ActiveUserModel? _currentUser;

  // بدء الخدمة
  Future<void> startService() async {
    print('🚀 بدء خدمة تتبع المستخدمين...');
    await _initializeRealUser();
    _startPeriodicUpdates();
    print('✅ خدمة تتبع المستخدمين جاهزة');
  }

  // إيقاف الخدمة
  void stopService() {
    _updateTimer?.cancel();
    _usersController.close();
    _notificationsController.close();
    print('⏹️ خدمة تتبع المستخدمين توقفت');
  }

  // الحصول على المستخدم الحالي
  Future<ActiveUserModel?> getCurrentUser() async {
    if (_currentUser != null) return _currentUser;

    try {
      // الحصول على الموقع الحالي
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // الحصول على العنوان
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      final placemark = placemarks.first;

      // الحصول على بيانات المستخدم من قاعدة البيانات
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getCurrentUserProfile();
      final displayName = userProfile != null
          ? userProfileService.getDisplayName(userProfile)
          : 'مستخدم';

      _currentUser = ActiveUserModel(
        id: 'current_user_real',
        name: displayName,
        email: userProfile?['email'] ?? '<EMAIL>',
        location: LatLng(position.latitude, position.longitude),
        address: '${placemark.locality}, ${placemark.country}',
        country: placemark.country ?? 'غير محدد',
        city: placemark.locality ?? 'غير محدد',
        lastSeen: DateTime.now(),
        isOnline: true,
        deviceType: 'Desktop',
        deviceModel: 'Windows PC',
        batteryLevel: 100,
        status: UserStatus.working,
        privacyLevel: PrivacyLevel.public,
        stats: UserStats(
          photosToday: 25,
          videosToday: 8,
          totalPhotos: 1250,
          totalVideos: 340,
          hoursActiveToday: 6.5,
          totalHoursActive: 450.0,
          sessionsToday: 3,
          totalSessions: 89,
          joinDate: DateTime.now().subtract(const Duration(days: 120)),
        ),
      );

      // لا نضيف المستخدم هنا - سيتم إضافته في _initializeRealUser

      print('📍 تم إضافة موقعك الحقيقي: ${position.latitude}, ${position.longitude}');
      print('🏠 العنوان: ${_currentUser!.address}');

      return _currentUser;
    } catch (e) {
      print('❌ خطأ في الحصول على المستخدم الحالي: $e');

      // إنشاء مستخدم افتراضي في صنعاء إذا فشل الحصول على الموقع
      final userProfileService = UserProfileService();
      final userProfile = await userProfileService.getCurrentUserProfile();
      final displayName = userProfile != null
          ? userProfileService.getDisplayName(userProfile)
          : 'مستخدم';

      _currentUser = ActiveUserModel(
        id: 'current_user_real',
        name: displayName,
        email: userProfile?['email'] ?? '<EMAIL>',
        location: const LatLng(15.3694, 44.1910), // صنعاء، اليمن
        address: 'صنعاء، اليمن',
        country: 'اليمن',
        city: 'صنعاء',
        lastSeen: DateTime.now(),
        isOnline: true,
        deviceType: 'Desktop',
        deviceModel: 'Windows PC',
        batteryLevel: 100,
        status: UserStatus.working,
        privacyLevel: PrivacyLevel.public,
        stats: UserStats(
          photosToday: 25,
          videosToday: 8,
          totalPhotos: 1250,
          totalVideos: 340,
          hoursActiveToday: 6.5,
          totalHoursActive: 450.0,
          sessionsToday: 3,
          totalSessions: 89,
          joinDate: DateTime.now().subtract(const Duration(days: 120)),
        ),
      );

      // لا نضيف المستخدم هنا - سيتم إضافته في _initializeRealUser فقط

      print('📍 تم إضافة موقع افتراضي في صنعاء');

      return _currentUser;
    }
  }

  // تهيئة جميع المستخدمين من قاعدة البيانات
  Future<void> _initializeRealUser() async {
    print('🔄 بدء تهيئة المستخدمين...');
    _activeUsers.clear();

    try {
      // الحصول على المستخدم الحقيقي (المدير)
      final currentUser = await getCurrentUser();

      // جلب جميع المستخدمين من قاعدة البيانات
      final allUsers = await _loadUsersFromDatabase();

      if (currentUser != null) {
        // إضافة المستخدم الحقيقي أولاً
        _activeUsers.add(currentUser);
      }

      // إضافة باقي المستخدمين من قاعدة البيانات
      _activeUsers.addAll(allUsers);

      // إرسال التحديث فوراً
      _usersController.add(List.from(_activeUsers));

      // إرسال إشعار
      _sendNotification(
        'مرحباً بك',
        'تم تسجيل دخولك من ${currentUser?.city ?? 'موقع غير محدد'}. يوجد ${_activeUsers.length} مستخدم متصل',
        'user_login',
      );

      print('✅ تم تهيئة ${_activeUsers.length} مستخدم');
      if (currentUser != null) {
        print('👤 المدير: ${currentUser.name} من ${currentUser.city}');
      }
      print('👥 مستخدمون آخرون: ${allUsers.length}');
      print('📊 إجمالي المستخدمين النشطين: ${_activeUsers.length}');

    } catch (e) {
      print('❌ خطأ في تهيئة المستخدمين: $e');
      _usersController.add([]);
    }
  }

  // جلب المستخدمين من قاعدة البيانات
  Future<List<ActiveUserModel>> _loadUsersFromDatabase() async {
    try {
      print('🔍 جلب المستخدمين من قاعدة البيانات...');

      // التأكد من تهيئة الخدمة الإدارية
      if (!AdminSupabaseService.instance.isInitialized) {
        await AdminSupabaseService.instance.initialize();
      }

      final supabase = AdminSupabaseService.instance.adminClient;

      // جلب جميع المستخدمين النشطين
      final response = await supabase
          .from('users')
          .select('id, full_name, email, phone, created_at, updated_at, last_login')
          .eq('is_active', true)
          .order('last_login', ascending: false);

      print('📊 تم جلب ${response.length} مستخدم من قاعدة البيانات');

      List<ActiveUserModel> users = [];

      for (int i = 0; i < response.length; i++) {
        final userData = response[i];

        // تخطي المستخدم الحالي (المدير) لأنه سيتم إضافته منفصلاً
        if (userData['email'] == '<EMAIL>') continue;

        // إنشاء مواقع متنوعة للمستخدمين
        final locations = [
          {'lat': 15.3694, 'lng': 44.1910, 'city': 'صنعاء', 'country': 'اليمن'},
          {'lat': 14.7937, 'lng': 42.9441, 'city': 'الحديدة', 'country': 'اليمن'},
          {'lat': 16.9402, 'lng': 43.7445, 'city': 'صعدة', 'country': 'اليمن'},
          {'lat': 13.5779, 'lng': 48.5160, 'city': 'عدن', 'country': 'اليمن'},
          {'lat': 14.5518, 'lng': 49.1233, 'city': 'المكلا', 'country': 'اليمن'},
        ];

        final location = locations[i % locations.length];

        final user = ActiveUserModel(
          id: userData['id'],
          name: userData['full_name'] ?? 'مستخدم ${i + 1}',
          email: userData['email'] ?? 'user${i + 1}@moonmemory.com',
          location: LatLng(location['lat']! as double, location['lng']! as double),
          address: '${location['city']}, ${location['country']}',
          country: location['country']! as String,
          city: location['city']! as String,
          lastSeen: userData['last_login'] != null
              ? DateTime.parse(userData['last_login'])
              : DateTime.now().subtract(Duration(minutes: i * 5)),
          isOnline: i < 2, // أول مستخدمين متصلين
          deviceType: i % 2 == 0 ? 'Mobile' : 'Desktop',
          deviceModel: i % 2 == 0 ? 'Android' : 'Windows PC',
          batteryLevel: 85 + (i * 3) % 15,
          status: UserStatus.values[i % UserStatus.values.length],
          privacyLevel: PrivacyLevel.public,
          stats: UserStats(
            photosToday: 10 + (i * 3),
            videosToday: 2 + i,
            totalPhotos: 100 + (i * 50),
            totalVideos: 20 + (i * 10),
            hoursActiveToday: 2.0 + (i * 0.5),
            totalHoursActive: 50.0 + (i * 20),
            sessionsToday: 1 + i,
            totalSessions: 10 + (i * 5),
            joinDate: DateTime.parse(userData['created_at']),
          ),
        );

        users.add(user);
      }

      print('✅ تم إنشاء ${users.length} مستخدم نشط');
      return users;

    } catch (e) {
      print('❌ خطأ في جلب المستخدمين من قاعدة البيانات: $e');
      return [];
    }
  }



  // تحديثات دورية للمستخدم الحقيقي فقط
  void _startPeriodicUpdates() {
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateRealUserStatus();
    });
  }

  // تحديث حالة المستخدم الحقيقي
  void _updateRealUserStatus() {
    if (_currentUser != null && _activeUsers.isNotEmpty) {
      // تحديث آخر ظهور للمستخدم الحقيقي
      final updatedUser = _currentUser!.copyWith(
        lastSeen: DateTime.now(),
        isOnline: true,
        batteryLevel: 100, // افتراض أن الكمبيوتر متصل بالكهرباء
      );

      _currentUser = updatedUser;
      _activeUsers[0] = updatedUser;
      _usersController.add(_activeUsers);

      print('🔄 تم تحديث حالة المستخدم الحقيقي');
    }
  }



  // إرسال إشعار
  void _sendNotification(String title, String message, String type, {Map<String, dynamic>? data}) {
    final notification = UserNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );
    
    _notificationsController.add(notification);
  }

  // الحصول على المستخدمين النشطين
  List<ActiveUserModel> get activeUsers => List.unmodifiable(_activeUsers);

  // الحصول على المستخدمين حسب البلد
  Map<String, List<ActiveUserModel>> getUsersByCountry() {
    final Map<String, List<ActiveUserModel>> result = {};
    
    for (final user in _activeUsers) {
      if (!result.containsKey(user.country)) {
        result[user.country] = [];
      }
      result[user.country]!.add(user);
    }
    
    return result;
  }

  // الحصول على إحصائيات عامة
  Map<String, dynamic> getGlobalStats() {
    final totalUsers = _activeUsers.length;
    final onlineUsers = _activeUsers.where((u) => u.isOnline).length;
    final activeUsers = _activeUsers.where((u) => u.isActive).length;
    final totalPhotosToday = _activeUsers.fold<int>(0, (sum, u) => sum + u.stats.photosToday);
    final totalVideosToday = _activeUsers.fold<int>(0, (sum, u) => sum + u.stats.videosToday);
    
    return {
      'totalUsers': totalUsers,
      'onlineUsers': onlineUsers,
      'activeUsers': activeUsers,
      'offlineUsers': totalUsers - onlineUsers,
      'totalPhotosToday': totalPhotosToday,
      'totalVideosToday': totalVideosToday,
      'totalMediaToday': totalPhotosToday + totalVideosToday,
      'countries': getUsersByCountry().length,
    };
  }
}
