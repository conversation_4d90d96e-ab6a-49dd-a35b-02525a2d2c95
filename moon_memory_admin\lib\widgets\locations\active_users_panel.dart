import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../models/active_user_model.dart';
import '../../providers/active_users_provider.dart';

class ActiveUsersPanel extends ConsumerStatefulWidget {
  final Function(ActiveUserModel)? onUserLocationTap;

  const ActiveUsersPanel({
    super.key,
    this.onUserLocationTap,
  });

  @override
  ConsumerState<ActiveUsersPanel> createState() => _ActiveUsersPanelState();
}

class _ActiveUsersPanelState extends ConsumerState<ActiveUsersPanel> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(activeUsersProvider);
    final filteredUsers = ref.watch(filteredUsersProvider);
    final globalStats = ref.watch(globalStatsProvider);
    final selectedUser = ref.watch(selectedUserProvider);

    // تسجيل للتتبع
    print('🎨 بناء لوحة المستخدمين - المستخدمون: ${state.users.length}');
    print('🔍 المستخدمون المفلترون: ${filteredUsers.length}');

    return Container(
      width: 400,
      margin: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // عنوان اللوحة
          _buildHeader(globalStats),

          // شريط البحث والفلاتر
          _buildSearchAndFilters(state),

          // قائمة المستخدمين
          Expanded(
            child: state.isLoading
                ? _buildLoadingView()
                : state.error != null
                    ? _buildErrorView(state.error!)
                    : _buildUsersList(filteredUsers, selectedUser),
          ),
        ],
      ),
    );
  }

  // عنوان اللوحة
  Widget _buildHeader(Map<String, dynamic> stats) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primaryGold.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.people,
                color: AppColors.primaryGold,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'المستخدمون النشطون',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryText,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.success,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${stats['onlineUsers'] ?? 0}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // إحصائيات سريعة
          Row(
            children: [
              _buildQuickStat('نشط', '${stats['activeUsers'] ?? 0}', Colors.green),
              const SizedBox(width: 12),
              _buildQuickStat('متصل', '${stats['onlineUsers'] ?? 0}', Colors.blue),
              const SizedBox(width: 12),
              _buildQuickStat('غير متصل', '${stats['offlineUsers'] ?? 0}', Colors.grey),
            ],
          ),
        ],
      ),
    );
  }

  // إحصائية سريعة
  Widget _buildQuickStat(String label, String value, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: AppColors.secondaryText,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // شريط البحث والفلاتر
  Widget _buildSearchAndFilters(ActiveUsersState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن مستخدم...',
              prefixIcon: Icon(Icons.search, color: AppColors.secondaryText),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        ref.read(activeUsersProvider.notifier).updateSearch('');
                      },
                      icon: Icon(Icons.clear, color: AppColors.secondaryText),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.primaryGold),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: (value) {
              ref.read(activeUsersProvider.notifier).updateSearch(value);
            },
          ),

          const SizedBox(height: 12),

          // فلاتر
          Row(
            children: [
              // فلتر المتصلين فقط
              Expanded(
                child: FilterChip(
                  label: const Text('متصل فقط'),
                  selected: state.showOnlineOnly,
                  onSelected: (selected) {
                    ref.read(activeUsersProvider.notifier).toggleOnlineOnly();
                  },
                  selectedColor: AppColors.primaryGold.withValues(alpha: 0.2),
                  checkmarkColor: AppColors.primaryGold,
                ),
              ),

              const SizedBox(width: 8),

              // فلتر البلد
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: state.selectedCountry.isEmpty ? null : state.selectedCountry,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  hint: const Text('البلد'),
                  items: [
                    const DropdownMenuItem(value: '', child: Text('جميع البلدان')),
                    ...state.availableCountries.map((country) {
                      return DropdownMenuItem(
                        value: country,
                        child: Text(country),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    ref.read(activeUsersProvider.notifier).updateSelectedCountry(value ?? '');
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // قائمة المستخدمين
  Widget _buildUsersList(List<ActiveUserModel> users, ActiveUserModel? selectedUser) {
    if (users.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.secondaryText,
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد مستخدمون',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.secondaryText,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        final isSelected = selectedUser?.id == user.id;
        
        return _buildUserCard(user, isSelected);
      },
    );
  }

  // بطاقة مستخدم
  Widget _buildUserCard(ActiveUserModel user, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primaryGold.withValues(alpha: 0.1) : AppColors.surfaceBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? AppColors.primaryGold : Colors.transparent,
          width: 2,
        ),
      ),
      child: ListTile(
        leading: Stack(
          children: [
            // صورة المستخدم
            CircleAvatar(
              radius: 20,
              backgroundImage: user.avatar != null ? NetworkImage(user.avatar!) : null,
              backgroundColor: _getUserStatusColor(user).withValues(alpha: 0.2),
              child: user.avatar == null
                  ? Icon(
                      Icons.person,
                      color: _getUserStatusColor(user),
                    )
                  : null,
            ),
            
            // مؤشر الحالة
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getUserStatusColor(user),
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
              ),
            ),
          ],
        ),
        
        title: Row(
          children: [
            Expanded(
              child: Text(
                user.name,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryText,
                ),
              ),
            ),
            // زر GPS للمستخدم الحقيقي
            if (user.id == 'current_user_real')
              Container(
                margin: const EdgeInsets.only(left: 8),
                child: IconButton(
                  onPressed: () {
                    // الانتقال إلى موقع المستخدم على الخريطة
                    _navigateToUserLocation(user);
                  },
                  icon: Icon(
                    Icons.my_location,
                    color: AppColors.primaryGold,
                    size: 20,
                  ),
                  tooltip: 'انتقل إلى موقعي',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ),
            Text(
              user.statusIcon,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${user.city}, ${user.country}',
              style: TextStyle(
                color: AppColors.secondaryText,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 2),
            Row(
              children: [
                Text(
                  user.statusDescription,
                  style: TextStyle(
                    color: _getUserStatusColor(user),
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  user.lastSeenText,
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ],
        ),
        
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (user.batteryLevel < 20)
              Icon(
                Icons.battery_alert,
                color: AppColors.error,
                size: 16,
              ),
            Text(
              '${user.batteryLevel}%',
              style: TextStyle(
                fontSize: 10,
                color: user.batteryLevel < 20 ? AppColors.error : AppColors.secondaryText,
              ),
            ),
          ],
        ),
        
        onTap: () {
          ref.read(activeUsersProvider.notifier).selectUser(user);
        },
      ),
    );
  }

  // عرض التحميل
  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.primaryGold),
          SizedBox(height: 16),
          Text('جاري تحميل المستخدمين...'),
        ],
      ),
    );
  }

  // عرض الخطأ
  Widget _buildErrorView(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل المستخدمين',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.read(activeUsersProvider.notifier).refresh(),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  // الحصول على لون حالة المستخدم
  Color _getUserStatusColor(ActiveUserModel user) {
    if (user.isActive) return AppColors.success;
    if (user.isIdle) return AppColors.warning;
    return AppColors.error;
  }

  // التنقل إلى موقع المستخدم على الخريطة
  void _navigateToUserLocation(ActiveUserModel user) {
    // تحديد المستخدم في المزود
    ref.read(activeUsersProvider.notifier).selectUser(user);

    // إرسال إشعار للخريطة للانتقال إلى الموقع
    if (widget.onUserLocationTap != null) {
      widget.onUserLocationTap!(user);
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🎯 الانتقال إلى موقع ${user.name}'),
        duration: const Duration(seconds: 1),
        backgroundColor: AppColors.primaryGold,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
