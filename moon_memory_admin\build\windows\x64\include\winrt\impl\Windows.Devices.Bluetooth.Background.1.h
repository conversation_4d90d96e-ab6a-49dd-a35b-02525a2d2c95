// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Devices_Bluetooth_Background_1_H
#define WINRT_Windows_Devices_Bluetooth_Background_1_H
#include "winrt/impl/Windows.Devices.Bluetooth.Background.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Bluetooth::Background
{
    struct __declspec(empty_bases) IBluetoothLEAdvertisementPublisherTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisherTriggerDetails>
    {
        IBluetoothLEAdvertisementPublisherTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisherTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementPublisherTriggerDetails2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisherTriggerDetails2>
    {
        IBluetoothLEAdvertisementPublisherTriggerDetails2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisherTriggerDetails2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementWatcherTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementWatcherTriggerDetails>
    {
        IBluetoothLEAdvertisementWatcherTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementWatcherTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGattCharacteristicNotificationTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattCharacteristicNotificationTriggerDetails>
    {
        IGattCharacteristicNotificationTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IGattCharacteristicNotificationTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGattCharacteristicNotificationTriggerDetails2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattCharacteristicNotificationTriggerDetails2>
    {
        IGattCharacteristicNotificationTriggerDetails2(std::nullptr_t = nullptr) noexcept {}
        IGattCharacteristicNotificationTriggerDetails2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGattServiceProviderConnection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattServiceProviderConnection>
    {
        IGattServiceProviderConnection(std::nullptr_t = nullptr) noexcept {}
        IGattServiceProviderConnection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGattServiceProviderConnectionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattServiceProviderConnectionStatics>
    {
        IGattServiceProviderConnectionStatics(std::nullptr_t = nullptr) noexcept {}
        IGattServiceProviderConnectionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IGattServiceProviderTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IGattServiceProviderTriggerDetails>
    {
        IGattServiceProviderTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IGattServiceProviderTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRfcommConnectionTriggerDetails :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommConnectionTriggerDetails>
    {
        IRfcommConnectionTriggerDetails(std::nullptr_t = nullptr) noexcept {}
        IRfcommConnectionTriggerDetails(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRfcommInboundConnectionInformation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommInboundConnectionInformation>
    {
        IRfcommInboundConnectionInformation(std::nullptr_t = nullptr) noexcept {}
        IRfcommInboundConnectionInformation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRfcommOutboundConnectionInformation :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRfcommOutboundConnectionInformation>
    {
        IRfcommOutboundConnectionInformation(std::nullptr_t = nullptr) noexcept {}
        IRfcommOutboundConnectionInformation(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
