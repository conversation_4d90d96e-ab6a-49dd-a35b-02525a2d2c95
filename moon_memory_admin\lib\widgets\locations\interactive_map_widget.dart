import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import '../../core/constants/app_colors.dart';
import '../../models/location_model.dart';
import '../../models/active_user_model.dart';
import '../../providers/locations_provider.dart';
import '../../providers/active_users_provider.dart';

class InteractiveMapWidget extends ConsumerStatefulWidget {
  final List<LocationModel> locations;

  const InteractiveMapWidget({
    super.key,
    required this.locations,
  });

  @override
  ConsumerState<InteractiveMapWidget> createState() => _InteractiveMapWidgetState();
}

class _InteractiveMapWidgetState extends ConsumerState<InteractiveMapWidget> {
  final MapController _mapController = MapController();

  // إحداثيات الرياض كمركز افتراضي
  static const LatLng _defaultCenter = LatLng(24.7136, 46.6753);
  static const double _defaultZoom = 11.0;

  // الموقع الحالي للمستخدم
  LatLng? _currentLocation;
  bool _isLoadingLocation = false;
  String? _locationError;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  @override
  Widget build(BuildContext context) {
    final selectedLocation = ref.watch(selectedLocationProvider);
    final activeUsers = ref.watch(filteredUsersProvider);
    final currentUser = ref.watch(currentUserProvider);
    final selectedUser = ref.watch(selectedUserProvider);

    // الانتقال إلى المستخدم المحدد تلقائياً
    ref.listen<ActiveUserModel?>(selectedUserProvider, (previous, next) {
      if (next != null && mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _animateToUser(next);
        });
      }
    });

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // الخريطة الأساسية
            FlutterMap(
              mapController: _mapController,
              options: MapOptions(
                initialCenter: _getMapCenter(),
                initialZoom: _defaultZoom,
                minZoom: 8.0,
                maxZoom: 18.0,
                onTap: (tapPosition, point) {
                  // إلغاء التحديد عند النقر على منطقة فارغة
                  ref.read(locationsProvider.notifier).clearSelection();
                },
              ),
              children: [
                // طبقة الخريطة الأساسية
                TileLayer(
                  urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                  userAgentPackageName: 'com.moonmemory.admin',
                  maxZoom: 18,
                ),

                // طبقة العلامات
                MarkerLayer(
                  markers: [
                    ..._buildMarkers(),
                    ..._buildActiveUsersMarkers(activeUsers),
                    if (_currentLocation != null) _buildCurrentLocationMarker(),
                    if (currentUser != null) _buildCurrentUserMarker(currentUser),
                  ],
                ),

                // طبقة الدوائر (نطاق التغطية)
                CircleLayer(
                  circles: _buildCoverageCircles(),
                ),
              ],
            ),

            // أدوات التحكم
            _buildMapControls(),

            // معلومات الخريطة
            _buildMapInfo(),
          ],
        ),
      ),
    );
  }

  // الحصول على مركز الخريطة
  LatLng _getMapCenter() {
    if (widget.locations.isEmpty) return _defaultCenter;

    // حساب المتوسط للإحداثيات
    double totalLat = 0;
    double totalLng = 0;
    int validLocations = 0;

    for (final location in widget.locations) {
      if (location.latitude != null && location.longitude != null) {
        totalLat += location.latitude!;
        totalLng += location.longitude!;
        validLocations++;
      }
    }

    if (validLocations == 0) return _defaultCenter;

    return LatLng(
      totalLat / validLocations,
      totalLng / validLocations,
    );
  }

  // بناء العلامات
  List<Marker> _buildMarkers() {
    final selectedLocation = ref.watch(selectedLocationProvider);
    
    return widget.locations
        .where((location) => location.latitude != null && location.longitude != null)
        .map((location) {
      final isSelected = selectedLocation?.id == location.id;
      
      return Marker(
        point: LatLng(location.latitude!, location.longitude!),
        width: isSelected ? 80 : 60,
        height: isSelected ? 80 : 60,
        child: GestureDetector(
          onTap: () {
            ref.read(locationsProvider.notifier).selectLocation(location);
            _animateToLocation(location);
          },
          child: _buildMarkerWidget(location, isSelected),
        ),
      );
    }).toList();
  }

  // بناء عنصر العلامة
  Widget _buildMarkerWidget(LocationModel location, bool isSelected) {
    final color = _getLocationColor(location);
    final size = isSelected ? 60.0 : 45.0;
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        border: Border.all(
          color: color,
          width: isSelected ? 4 : 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getLocationIcon(location),
            color: color,
            size: isSelected ? 24 : 18,
          ),
          if (isSelected) ...[
            const SizedBox(height: 2),
            Text(
              '${location.devicesCount ?? 0}',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // بناء دوائر التغطية
  List<CircleMarker> _buildCoverageCircles() {
    final selectedLocation = ref.watch(selectedLocationProvider);
    
    if (selectedLocation?.latitude == null || selectedLocation?.longitude == null) {
      return [];
    }

    return [
      CircleMarker(
        point: LatLng(selectedLocation!.latitude!, selectedLocation.longitude!),
        radius: 500, // نطاق 500 متر
        color: _getLocationColor(selectedLocation).withValues(alpha: 0.1),
        borderColor: _getLocationColor(selectedLocation).withValues(alpha: 0.3),
        borderStrokeWidth: 2,
      ),
    ];
  }

  // أدوات التحكم في الخريطة
  Widget _buildMapControls() {
    return Positioned(
      top: 16,
      right: 16,
      child: Column(
        children: [
          // زر التكبير
          _buildControlButton(
            icon: Icons.add,
            onPressed: () => _mapController.move(
              _mapController.camera.center,
              _mapController.camera.zoom + 1,
            ),
          ),
          const SizedBox(height: 8),
          
          // زر التصغير
          _buildControlButton(
            icon: Icons.remove,
            onPressed: () => _mapController.move(
              _mapController.camera.center,
              _mapController.camera.zoom - 1,
            ),
          ),
          const SizedBox(height: 8),
          
          // زر العودة للمركز
          _buildControlButton(
            icon: Icons.my_location,
            onPressed: () => _mapController.move(
              _getMapCenter(),
              _defaultZoom,
            ),
          ),
          const SizedBox(height: 8),
          
          // زر ملء الشاشة
          _buildControlButton(
            icon: Icons.fullscreen,
            onPressed: () => _fitBounds(),
          ),
        ],
      ),
    );
  }

  // زر تحكم
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: AppColors.primaryGold,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGold.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Colors.white,
          width: 2,
        ),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, size: 22),
        color: Colors.white,
        padding: EdgeInsets.zero,
        splashRadius: 20,
      ),
    );
  }

  // معلومات الخريطة
  Widget _buildMapInfo() {
    return Positioned(
      bottom: 16,
      left: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.info_outline,
              size: 16,
              color: AppColors.primaryGold,
            ),
            const SizedBox(width: 8),
            Text(
              '${widget.locations.length} موقع',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.primaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // تحريك الخريطة إلى موقع معين
  void _animateToLocation(LocationModel location) {
    if (location.latitude != null && location.longitude != null) {
      _mapController.move(
        LatLng(location.latitude!, location.longitude!),
        15.0, // تكبير أكثر عند التحديد
      );
    }
  }

  // ملء الشاشة بجميع المواقع
  void _fitBounds() {
    final validLocations = widget.locations
        .where((l) => l.latitude != null && l.longitude != null)
        .toList();

    if (validLocations.isEmpty) return;

    if (validLocations.length == 1) {
      final location = validLocations.first;
      _mapController.move(
        LatLng(location.latitude!, location.longitude!),
        15.0,
      );
      return;
    }

    // حساب الحدود
    double minLat = validLocations.first.latitude!;
    double maxLat = validLocations.first.latitude!;
    double minLng = validLocations.first.longitude!;
    double maxLng = validLocations.first.longitude!;

    for (final location in validLocations) {
      minLat = minLat < location.latitude! ? minLat : location.latitude!;
      maxLat = maxLat > location.latitude! ? maxLat : location.latitude!;
      minLng = minLng < location.longitude! ? minLng : location.longitude!;
      maxLng = maxLng > location.longitude! ? maxLng : location.longitude!;
    }

    final bounds = LatLngBounds(
      LatLng(minLat, minLng),
      LatLng(maxLat, maxLng),
    );

    _mapController.fitCamera(
      CameraFit.bounds(
        bounds: bounds,
        padding: const EdgeInsets.all(50),
      ),
    );
  }

  // الحصول على لون الموقع
  Color _getLocationColor(LocationModel location) {
    if (location.isULocation) {
      return AppColors.info;
    } else {
      return AppColors.success;
    }
  }

  // الحصول على أيقونة الموقع
  IconData _getLocationIcon(LocationModel location) {
    if (location.isULocation) {
      return Icons.account_balance;
    } else {
      return Icons.school;
    }
  }

  // الحصول على الموقع الحالي
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
      _locationError = null;
    });

    try {
      // التحقق من الصلاحيات
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw 'خدمات الموقع غير مفعلة';
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw 'تم رفض صلاحيات الموقع';
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw 'صلاحيات الموقع مرفوضة نهائياً';
      }

      // الحصول على الموقع
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      if (mounted) {
        setState(() {
          _currentLocation = LatLng(position.latitude, position.longitude);
          _isLoadingLocation = false;
        });
      }

      print('📍 تم الحصول على الموقع الحالي: ${position.latitude}, ${position.longitude}');

    } catch (e) {
      if (mounted) {
        setState(() {
          _locationError = e.toString();
          _isLoadingLocation = false;
        });
      }
      print('❌ خطأ في الحصول على الموقع: $e');
    }
  }

  // الانتقال إلى الموقع الحالي
  void _goToCurrentLocation() {
    if (_currentLocation != null) {
      _mapController.move(_currentLocation!, 15.0);
    }
  }

  // بناء علامة الموقع الحالي
  Marker _buildCurrentLocationMarker() {
    return Marker(
      point: _currentLocation!,
      width: 80,
      height: 80,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.blue,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Icon(
          Icons.my_location,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  // بناء علامات المستخدمين النشطين
  List<Marker> _buildActiveUsersMarkers(List<ActiveUserModel> users) {
    return users.map((user) {
      return Marker(
        point: user.location,
        width: 70,
        height: 70,
        child: GestureDetector(
          onTap: () {
            ref.read(activeUsersProvider.notifier).selectUser(user);
            _animateToUser(user);
          },
          child: _buildActiveUserMarker(user),
        ),
      );
    }).toList();
  }

  // بناء علامة مستخدم نشط
  Widget _buildActiveUserMarker(ActiveUserModel user) {
    final color = _getUserStatusColor(user);

    return Container(
      width: 70,
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        border: Border.all(color: color, width: 3),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // صورة المستخدم أو أيقونة
          Center(
            child: user.avatar != null
                ? ClipOval(
                    child: Image.network(
                      user.avatar!,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.person,
                          color: color,
                          size: 30,
                        );
                      },
                    ),
                  )
                : Icon(
                    Icons.person,
                    color: color,
                    size: 30,
                  ),
          ),

          // مؤشر الحالة
          Positioned(
            bottom: 5,
            right: 5,
            child: Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
            ),
          ),

          // أيقونة النشاط
          if (user.status != UserStatus.available)
            Positioned(
              top: 2,
              left: 2,
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(color: color, width: 1),
                ),
                child: Center(
                  child: Text(
                    user.statusIcon,
                    style: const TextStyle(fontSize: 10),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // بناء علامة المستخدم الحالي
  Marker _buildCurrentUserMarker(ActiveUserModel user) {
    return Marker(
      point: user.location,
      width: 90,
      height: 90,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.primaryGold,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 4),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryGold.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Icon(
          Icons.person_pin,
          color: Colors.white,
          size: 40,
        ),
      ),
    );
  }

  // الحصول على لون حالة المستخدم
  Color _getUserStatusColor(ActiveUserModel user) {
    if (user.isActive) return Colors.green;
    if (user.isIdle) return Colors.orange;
    return Colors.red;
  }

  // تحريك الخريطة إلى مستخدم معين
  void _animateToUser(ActiveUserModel user) {
    _mapController.move(user.location, 16.0);
  }

  // دالة عامة للانتقال إلى موقع مستخدم
  void animateToUserLocation(ActiveUserModel user) {
    _animateToUser(user);
  }
}
