// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Devices_1_H
#define WINRT_Windows_Devices_1_H
#include "winrt/impl/Windows.Devices.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices
{
    struct __declspec(empty_bases) ILowLevelDevicesAggregateProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILowLevelDevicesAggregateProvider>
    {
        ILowLevelDevicesAggregateProvider(std::nullptr_t = nullptr) noexcept {}
        ILowLevelDevicesAggregateProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILowLevelDevicesAggregateProviderFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILowLevelDevicesAggregateProviderFactory>
    {
        ILowLevelDevicesAggregateProviderFactory(std::nullptr_t = nullptr) noexcept {}
        ILowLevelDevicesAggregateProviderFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILowLevelDevicesController :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILowLevelDevicesController>
    {
        ILowLevelDevicesController(std::nullptr_t = nullptr) noexcept {}
        ILowLevelDevicesController(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ILowLevelDevicesControllerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ILowLevelDevicesControllerStatics>
    {
        ILowLevelDevicesControllerStatics(std::nullptr_t = nullptr) noexcept {}
        ILowLevelDevicesControllerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
