import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/location_model.dart';
import '../models/device_model.dart';
import '../services/locations_service.dart';

// حالة المواقع
class LocationsState {
  final List<LocationModel> locations;
  final LocationModel? selectedLocation;
  final List<DeviceModel> selectedLocationDevices;
  final Map<String, dynamic> stats;
  final bool isLoading;
  final String? error;
  final bool isMapReady;

  const LocationsState({
    this.locations = const [],
    this.selectedLocation,
    this.selectedLocationDevices = const [],
    this.stats = const {},
    this.isLoading = false,
    this.error,
    this.isMapReady = false,
  });

  LocationsState copyWith({
    List<LocationModel>? locations,
    LocationModel? selectedLocation,
    List<DeviceModel>? selectedLocationDevices,
    Map<String, dynamic>? stats,
    bool? isLoading,
    String? error,
    bool? isMapReady,
  }) {
    return LocationsState(
      locations: locations ?? this.locations,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      selectedLocationDevices: selectedLocationDevices ?? this.selectedLocationDevices,
      stats: stats ?? this.stats,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isMapReady: isMapReady ?? this.isMapReady,
    );
  }
}

// مزود المواقع
class LocationsNotifier extends StateNotifier<LocationsState> {
  final LocationsService _locationsService = LocationsService();

  LocationsNotifier() : super(const LocationsState()) {
    loadLocations();
  }

  /// تحميل المواقع
  Future<void> loadLocations() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final locations = await _locationsService.getLocationsWithCoordinates();
      final stats = await _locationsService.getLocationsStats();

      state = state.copyWith(
        locations: locations,
        stats: stats,
        isLoading: false,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  /// اختيار موقع معين
  Future<void> selectLocation(LocationModel location) async {
    state = state.copyWith(selectedLocation: location);

    try {
      final devices = await _locationsService.getDevicesInLocation(location.id);
      state = state.copyWith(selectedLocationDevices: devices);
    } catch (error) {
      print('❌ خطأ في جلب أجهزة الموقع: $error');
    }
  }

  /// إلغاء اختيار الموقع
  void clearSelection() {
    state = state.copyWith(
      selectedLocation: null,
      selectedLocationDevices: [],
    );
  }

  /// تحديد أن الخريطة جاهزة
  void setMapReady(bool isReady) {
    state = state.copyWith(isMapReady: isReady);
  }

  /// تحديث المواقع
  void refresh() {
    loadLocations();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Getters مفيدة
  List<LocationModel> get activeLocations => 
      state.locations.where((location) => location.isActive).toList();

  List<LocationModel> get inactiveLocations => 
      state.locations.where((location) => !location.isActive).toList();

  List<LocationModel> get universityLocations => 
      state.locations.where((location) => location.isULocation).toList();

  List<LocationModel> get collegeLocations => 
      state.locations.where((location) => location.isCLocation).toList();

  int get totalDevicesCount => 
      state.locations.fold<int>(0, (sum, location) => sum + (location.devicesCount ?? 0));

  int get totalMediaCount => 
      state.locations.fold<int>(0, (sum, location) => sum + location.totalMedia);
}

// مزود المواقع
final locationsProvider = StateNotifierProvider<LocationsNotifier, LocationsState>((ref) {
  return LocationsNotifier();
});

// مزودات مساعدة
final activeLocationsProvider = Provider<List<LocationModel>>((ref) {
  final locationsState = ref.watch(locationsProvider);
  return locationsState.locations.where((location) => location.isActive).toList();
});

final selectedLocationProvider = Provider<LocationModel?>((ref) {
  final locationsState = ref.watch(locationsProvider);
  return locationsState.selectedLocation;
});

final selectedLocationDevicesProvider = Provider<List<DeviceModel>>((ref) {
  final locationsState = ref.watch(locationsProvider);
  return locationsState.selectedLocationDevices;
});

final locationsStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final locationsState = ref.watch(locationsProvider);
  return locationsState.stats;
});
