import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/photo_model.dart';
import '../models/video_model.dart';

class MediaService {
  static final MediaService _instance = MediaService._internal();
  factory MediaService() => _instance;
  MediaService._internal();

  static MediaService get instance => _instance;

  final SupabaseClient _supabase = Supabase.instance.client;

  /// جلب الصور مع الفلترة والبحث
  Future<Map<String, dynamic>> getPhotos({
    int page = 1,
    int limit = 20,
    String? search,
    String? userId,
    String? deviceId,
    String? locationId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      print('🖼️ جلب الصور - الصفحة: $page، الحد: $limit');

      var query = _supabase
          .from('photos')
          .select('''
            *,
            users!photos_user_id_fkey(id, full_name, national_id),
            devices!photos_device_id_fkey(id, device_name, device_type),
            locations!photos_location_id_fkey(id, name, address)
          ''');

      // تطبيق الفلاتر
      if (search?.isNotEmpty == true) {
        query = query.or('file_name.ilike.%$search%,description.ilike.%$search%');
      }

      if (userId?.isNotEmpty == true) {
        query = query.eq('user_id', userId!);
      }

      if (deviceId?.isNotEmpty == true) {
        query = query.eq('device_id', deviceId!);
      }

      if (locationId?.isNotEmpty == true) {
        query = query.eq('location_id', locationId!);
      }

      if (startDate != null) {
        query = query.gte('created_at', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.lte('created_at', endDate.toIso8601String());
      }

      // ترتيب وتقسيم الصفحات
      final countQuery = await query.count(CountOption.exact);
      final totalCount = countQuery.count;

      final response = await query
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      final photos = (response as List)
          .map((json) => PhotoModel.fromJson(json))
          .toList();

      print('✅ تم جلب ${photos.length} صورة من أصل $totalCount');

      return {
        'success': true,
        'photos': photos,
        'totalCount': totalCount,
        'currentPage': page,
        'totalPages': (totalCount / limit).ceil(),
        'hasMore': page * limit < totalCount,
      };
    } catch (error) {
      print('❌ خطأ في جلب الصور: $error');
      return {
        'success': false,
        'error': 'فشل في جلب الصور: ${error.toString()}',
        'photos': <PhotoModel>[],
        'totalCount': 0,
      };
    }
  }

  /// جلب الفيديوهات مع الفلترة والبحث
  Future<Map<String, dynamic>> getVideos({
    int page = 1,
    int limit = 20,
    String? search,
    String? userId,
    String? deviceId,
    String? locationId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      print('🎬 جلب الفيديوهات - الصفحة: $page، الحد: $limit');

      var query = _supabase
          .from('videos')
          .select('''
            *,
            users!videos_user_id_fkey(id, full_name, national_id),
            devices!videos_device_id_fkey(id, device_name, device_type),
            locations!videos_location_id_fkey(id, name, address)
          ''');

      // تطبيق الفلاتر
      if (search?.isNotEmpty == true) {
        query = query.or('file_name.ilike.%$search%,description.ilike.%$search%');
      }

      if (userId?.isNotEmpty == true) {
        query = query.eq('user_id', userId!);
      }

      if (deviceId?.isNotEmpty == true) {
        query = query.eq('device_id', deviceId!);
      }

      if (locationId?.isNotEmpty == true) {
        query = query.eq('location_id', locationId!);
      }

      if (startDate != null) {
        query = query.gte('created_at', startDate.toIso8601String());
      }

      if (endDate != null) {
        query = query.lte('created_at', endDate.toIso8601String());
      }

      // ترتيب وتقسيم الصفحات
      final countQuery = await query.count(CountOption.exact);
      final totalCount = countQuery.count;

      final response = await query
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      final videos = (response as List)
          .map((json) => VideoModel.fromJson(json))
          .toList();

      print('✅ تم جلب ${videos.length} فيديو من أصل $totalCount');

      return {
        'success': true,
        'videos': videos,
        'totalCount': totalCount,
        'currentPage': page,
        'totalPages': (totalCount / limit).ceil(),
        'hasMore': page * limit < totalCount,
      };
    } catch (error) {
      print('❌ خطأ في جلب الفيديوهات: $error');
      return {
        'success': false,
        'error': 'فشل في جلب الفيديوهات: ${error.toString()}',
        'videos': <VideoModel>[],
        'totalCount': 0,
      };
    }
  }

  /// حذف صورة
  Future<Map<String, dynamic>> deletePhoto({
    required String photoId,
  }) async {
    try {
      print('🗑️ حذف الصورة: $photoId');

      // جلب معلومات الصورة أولاً
      final photo = await _supabase
          .from('photos')
          .select('file_path')
          .eq('id', photoId)
          .single();

      // حذف الملف من التخزين
      if (photo['file_path'] != null) {
        await _supabase.storage
            .from('photos')
            .remove([photo['file_path']]);
      }

      // حذف السجل من قاعدة البيانات
      await _supabase.from('photos').delete().eq('id', photoId);

      print('✅ تم حذف الصورة بنجاح');

      return {
        'success': true,
        'message': 'تم حذف الصورة بنجاح',
      };
    } catch (error) {
      print('❌ خطأ في حذف الصورة: $error');
      return {
        'success': false,
        'error': 'فشل في حذف الصورة: ${error.toString()}',
      };
    }
  }

  /// حذف فيديو
  Future<Map<String, dynamic>> deleteVideo({
    required String videoId,
  }) async {
    try {
      print('🗑️ حذف الفيديو: $videoId');

      // جلب معلومات الفيديو أولاً
      final video = await _supabase
          .from('videos')
          .select('file_path')
          .eq('id', videoId)
          .single();

      // حذف الملف من التخزين
      if (video['file_path'] != null) {
        await _supabase.storage
            .from('videos')
            .remove([video['file_path']]);
      }

      // حذف السجل من قاعدة البيانات
      await _supabase.from('videos').delete().eq('id', videoId);

      print('✅ تم حذف الفيديو بنجاح');

      return {
        'success': true,
        'message': 'تم حذف الفيديو بنجاح',
      };
    } catch (error) {
      print('❌ خطأ في حذف الفيديو: $error');
      return {
        'success': false,
        'error': 'فشل في حذف الفيديو: ${error.toString()}',
      };
    }
  }

  /// تحديث وصف الصورة
  Future<Map<String, dynamic>> updatePhotoDescription({
    required String photoId,
    required String description,
  }) async {
    try {
      print('🔄 تحديث وصف الصورة: $photoId');

      final response = await _supabase
          .from('photos')
          .update({
            'description': description,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', photoId)
          .select('''
            *,
            users!photos_user_id_fkey(id, full_name, national_id),
            devices!photos_device_id_fkey(id, device_name, device_type),
            locations!photos_location_id_fkey(id, name, address)
          ''')
          .single();

      final photo = PhotoModel.fromJson(response);

      print('✅ تم تحديث وصف الصورة بنجاح');

      return {
        'success': true,
        'photo': photo,
        'message': 'تم تحديث وصف الصورة بنجاح',
      };
    } catch (error) {
      print('❌ خطأ في تحديث وصف الصورة: $error');
      return {
        'success': false,
        'error': 'فشل في تحديث وصف الصورة: ${error.toString()}',
      };
    }
  }

  /// تحديث وصف الفيديو
  Future<Map<String, dynamic>> updateVideoDescription({
    required String videoId,
    required String description,
  }) async {
    try {
      print('🔄 تحديث وصف الفيديو: $videoId');

      final response = await _supabase
          .from('videos')
          .update({
            'description': description,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', videoId)
          .select('''
            *,
            users!videos_user_id_fkey(id, full_name, national_id),
            devices!videos_device_id_fkey(id, device_name, device_type),
            locations!videos_location_id_fkey(id, name, address)
          ''')
          .single();

      final video = VideoModel.fromJson(response);

      print('✅ تم تحديث وصف الفيديو بنجاح');

      return {
        'success': true,
        'video': video,
        'message': 'تم تحديث وصف الفيديو بنجاح',
      };
    } catch (error) {
      print('❌ خطأ في تحديث وصف الفيديو: $error');
      return {
        'success': false,
        'error': 'فشل في تحديث وصف الفيديو: ${error.toString()}',
      };
    }
  }

  /// جلب إحصائيات الوسائط
  Future<Map<String, dynamic>> getMediaStats() async {
    try {
      print('📊 جلب إحصائيات الوسائط');

      final totalPhotos = await _supabase
          .from('photos')
          .select('id')
          .count(CountOption.exact);

      final totalVideos = await _supabase
          .from('videos')
          .select('id')
          .count(CountOption.exact);

      // حساب حجم التخزين (تقريبي)
      final photosSize = await _supabase
          .from('photos')
          .select('file_size_bytes')
          .then((data) => (data as List).fold<int>(0, (sum, item) => sum + (item['file_size_bytes'] as int? ?? 0)));

      final videosSize = await _supabase
          .from('videos')
          .select('file_size_bytes')
          .then((data) => (data as List).fold<int>(0, (sum, item) => sum + (item['file_size_bytes'] as int? ?? 0)));

      print('✅ تم جلب إحصائيات الوسائط');

      return {
        'success': true,
        'totalPhotos': totalPhotos.count,
        'totalVideos': totalVideos.count,
        'totalMedia': totalPhotos.count + totalVideos.count,
        'photosSize': photosSize,
        'videosSize': videosSize,
        'totalSize': photosSize + videosSize,
      };
    } catch (error) {
      print('❌ خطأ في جلب إحصائيات الوسائط: $error');
      return {
        'success': false,
        'error': 'فشل في جلب إحصائيات الوسائط: ${error.toString()}',
        'totalPhotos': 0,
        'totalVideos': 0,
        'totalMedia': 0,
        'photosSize': 0,
        'videosSize': 0,
        'totalSize': 0,
      };
    }
  }
}
