// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_UserActivities_Core_1_H
#define WINRT_Windows_ApplicationModel_UserActivities_Core_1_H
#include "winrt/impl/Windows.ApplicationModel.UserActivities.Core.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::UserActivities::Core
{
    struct __declspec(empty_bases) ICoreUserActivityManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ICoreUserActivityManagerStatics>
    {
        ICoreUserActivityManagerStatics(std::nullptr_t = nullptr) noexcept {}
        ICoreUserActivityManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
