// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Devices_Bluetooth_Advertisement_1_H
#define WINRT_Windows_Devices_Bluetooth_Advertisement_1_H
#include "winrt/impl/Windows.Devices.Bluetooth.Advertisement.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::Bluetooth::Advertisement
{
    struct __declspec(empty_bases) IBluetoothLEAdvertisement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisement>
    {
        IBluetoothLEAdvertisement(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementBytePattern :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementBytePattern>
    {
        IBluetoothLEAdvertisementBytePattern(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementBytePattern(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementBytePatternFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementBytePatternFactory>
    {
        IBluetoothLEAdvertisementBytePatternFactory(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementBytePatternFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementDataSection :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementDataSection>
    {
        IBluetoothLEAdvertisementDataSection(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementDataSection(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementDataSectionFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementDataSectionFactory>
    {
        IBluetoothLEAdvertisementDataSectionFactory(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementDataSectionFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementDataTypesStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementDataTypesStatics>
    {
        IBluetoothLEAdvertisementDataTypesStatics(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementDataTypesStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementFilter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementFilter>
    {
        IBluetoothLEAdvertisementFilter(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementFilter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementPublisher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisher>
    {
        IBluetoothLEAdvertisementPublisher(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementPublisher2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisher2>
    {
        IBluetoothLEAdvertisementPublisher2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisher2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementPublisherFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisherFactory>
    {
        IBluetoothLEAdvertisementPublisherFactory(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisherFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementPublisherStatusChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisherStatusChangedEventArgs>
    {
        IBluetoothLEAdvertisementPublisherStatusChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisherStatusChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementPublisherStatusChangedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementPublisherStatusChangedEventArgs2>
    {
        IBluetoothLEAdvertisementPublisherStatusChangedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementPublisherStatusChangedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementReceivedEventArgs>
    {
        IBluetoothLEAdvertisementReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementReceivedEventArgs2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementReceivedEventArgs2>
    {
        IBluetoothLEAdvertisementReceivedEventArgs2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementReceivedEventArgs2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementWatcher :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementWatcher>
    {
        IBluetoothLEAdvertisementWatcher(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementWatcher(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementWatcher2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementWatcher2>
    {
        IBluetoothLEAdvertisementWatcher2(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementWatcher2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementWatcherFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementWatcherFactory>
    {
        IBluetoothLEAdvertisementWatcherFactory(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementWatcherFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEAdvertisementWatcherStoppedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEAdvertisementWatcherStoppedEventArgs>
    {
        IBluetoothLEAdvertisementWatcherStoppedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEAdvertisementWatcherStoppedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEManufacturerData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEManufacturerData>
    {
        IBluetoothLEManufacturerData(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEManufacturerData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IBluetoothLEManufacturerDataFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IBluetoothLEManufacturerDataFactory>
    {
        IBluetoothLEManufacturerDataFactory(std::nullptr_t = nullptr) noexcept {}
        IBluetoothLEManufacturerDataFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
