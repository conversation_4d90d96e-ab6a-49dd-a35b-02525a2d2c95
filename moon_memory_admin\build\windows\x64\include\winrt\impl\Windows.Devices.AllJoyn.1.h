// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Devices_AllJoyn_1_H
#define WINRT_Windows_Devices_AllJoyn_1_H
#include "winrt/impl/Windows.Devices.AllJoyn.0.h"
WINRT_EXPORT namespace winrt::Windows::Devices::AllJoyn
{
    struct __declspec(empty_bases) IAllJoynAboutData :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAboutData>
    {
        IAllJoynAboutData(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAboutData(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynAboutDataView :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAboutDataView>
    {
        IAllJoynAboutDataView(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAboutDataView(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynAboutDataViewStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAboutDataViewStatics>
    {
        IAllJoynAboutDataViewStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAboutDataViewStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynAcceptSessionJoiner :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAcceptSessionJoiner>
    {
        IAllJoynAcceptSessionJoiner(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAcceptSessionJoiner(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynAcceptSessionJoinerEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAcceptSessionJoinerEventArgs>
    {
        IAllJoynAcceptSessionJoinerEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAcceptSessionJoinerEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynAcceptSessionJoinerEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAcceptSessionJoinerEventArgsFactory>
    {
        IAllJoynAcceptSessionJoinerEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAcceptSessionJoinerEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynAuthenticationCompleteEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynAuthenticationCompleteEventArgs>
    {
        IAllJoynAuthenticationCompleteEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynAuthenticationCompleteEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusAttachment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachment>
    {
        IAllJoynBusAttachment(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusAttachment2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachment2>
    {
        IAllJoynBusAttachment2(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachment2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusAttachmentFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachmentFactory>
    {
        IAllJoynBusAttachmentFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachmentFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusAttachmentStateChangedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachmentStateChangedEventArgs>
    {
        IAllJoynBusAttachmentStateChangedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachmentStateChangedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusAttachmentStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusAttachmentStatics>
    {
        IAllJoynBusAttachmentStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusAttachmentStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusObject :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusObject>
    {
        IAllJoynBusObject(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusObject(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusObjectFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusObjectFactory>
    {
        IAllJoynBusObjectFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusObjectFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusObjectStoppedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusObjectStoppedEventArgs>
    {
        IAllJoynBusObjectStoppedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusObjectStoppedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynBusObjectStoppedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynBusObjectStoppedEventArgsFactory>
    {
        IAllJoynBusObjectStoppedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynBusObjectStoppedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynCredentials :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynCredentials>
    {
        IAllJoynCredentials(std::nullptr_t = nullptr) noexcept {}
        IAllJoynCredentials(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynCredentialsRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynCredentialsRequestedEventArgs>
    {
        IAllJoynCredentialsRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynCredentialsRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynCredentialsVerificationRequestedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynCredentialsVerificationRequestedEventArgs>
    {
        IAllJoynCredentialsVerificationRequestedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynCredentialsVerificationRequestedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynMessageInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynMessageInfo>
    {
        IAllJoynMessageInfo(std::nullptr_t = nullptr) noexcept {}
        IAllJoynMessageInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynMessageInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynMessageInfoFactory>
    {
        IAllJoynMessageInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynMessageInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynProducer :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynProducer>
    {
        IAllJoynProducer(std::nullptr_t = nullptr) noexcept {}
        IAllJoynProducer(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynProducerStoppedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynProducerStoppedEventArgs>
    {
        IAllJoynProducerStoppedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynProducerStoppedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynProducerStoppedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynProducerStoppedEventArgsFactory>
    {
        IAllJoynProducerStoppedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynProducerStoppedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynServiceInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfo>
    {
        IAllJoynServiceInfo(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynServiceInfoFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfoFactory>
    {
        IAllJoynServiceInfoFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfoFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynServiceInfoRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfoRemovedEventArgs>
    {
        IAllJoynServiceInfoRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfoRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynServiceInfoRemovedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfoRemovedEventArgsFactory>
    {
        IAllJoynServiceInfoRemovedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfoRemovedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynServiceInfoStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynServiceInfoStatics>
    {
        IAllJoynServiceInfoStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynServiceInfoStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSession :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSession>
    {
        IAllJoynSession(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSession(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionJoinedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionJoinedEventArgs>
    {
        IAllJoynSessionJoinedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionJoinedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionJoinedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionJoinedEventArgsFactory>
    {
        IAllJoynSessionJoinedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionJoinedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionLostEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionLostEventArgs>
    {
        IAllJoynSessionLostEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionLostEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionLostEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionLostEventArgsFactory>
    {
        IAllJoynSessionLostEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionLostEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionMemberAddedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionMemberAddedEventArgs>
    {
        IAllJoynSessionMemberAddedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionMemberAddedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionMemberAddedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionMemberAddedEventArgsFactory>
    {
        IAllJoynSessionMemberAddedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionMemberAddedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionMemberRemovedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionMemberRemovedEventArgs>
    {
        IAllJoynSessionMemberRemovedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionMemberRemovedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionMemberRemovedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionMemberRemovedEventArgsFactory>
    {
        IAllJoynSessionMemberRemovedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionMemberRemovedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynSessionStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynSessionStatics>
    {
        IAllJoynSessionStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynSessionStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynStatusStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynStatusStatics>
    {
        IAllJoynStatusStatics(std::nullptr_t = nullptr) noexcept {}
        IAllJoynStatusStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynWatcherStoppedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynWatcherStoppedEventArgs>
    {
        IAllJoynWatcherStoppedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IAllJoynWatcherStoppedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IAllJoynWatcherStoppedEventArgsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAllJoynWatcherStoppedEventArgsFactory>
    {
        IAllJoynWatcherStoppedEventArgsFactory(std::nullptr_t = nullptr) noexcept {}
        IAllJoynWatcherStoppedEventArgsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
