import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/devices_provider.dart';
import '../../providers/users_provider.dart';

class CreateDeviceDialog extends ConsumerStatefulWidget {
  const CreateDeviceDialog({super.key});

  @override
  ConsumerState<CreateDeviceDialog> createState() => _CreateDeviceDialogState();
}

class _CreateDeviceDialogState extends ConsumerState<CreateDeviceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _deviceIdController = TextEditingController();
  final _deviceNameController = TextEditingController();
  final _modelController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedDeviceType = 'camera';
  String? _selectedUserId;
  String? _selectedLocationId;
  bool _isLoading = false;

  final List<Map<String, dynamic>> _deviceTypes = [
    {'value': 'camera', 'label': 'كاميرا', 'icon': Icons.camera_alt},
    {'value': 'sensor', 'label': 'مستشعر', 'icon': Icons.sensors},
    {'value': 'recorder', 'label': 'مسجل', 'icon': Icons.mic},
    {'value': 'tracker', 'label': 'متتبع', 'icon': Icons.gps_fixed},
  ];

  @override
  void dispose() {
    _deviceIdController.dispose();
    _deviceNameController.dispose();
    _modelController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final usersState = ref.watch(usersProvider);

    return Dialog(
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس النافذة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.primaryGold.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.add_circle,
                    color: AppColors.primaryGold,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'إضافة جهاز جديد',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryText,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    color: AppColors.secondaryText,
                  ),
                ],
              ),
            ),

            // محتوى النافذة
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معرف الجهاز
                      TextFormField(
                        controller: _deviceIdController,
                        decoration: InputDecoration(
                          labelText: 'معرف الجهاز *',
                          hintText: 'أدخل معرف الجهاز الفريد',
                          prefixIcon: const Icon(Icons.tag),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'معرف الجهاز مطلوب';
                          }
                          if (value!.length < 3) {
                            return 'معرف الجهاز يجب أن يكون 3 أحرف على الأقل';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // اسم الجهاز
                      TextFormField(
                        controller: _deviceNameController,
                        decoration: InputDecoration(
                          labelText: 'اسم الجهاز *',
                          hintText: 'أدخل اسم الجهاز',
                          prefixIcon: const Icon(Icons.devices),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'اسم الجهاز مطلوب';
                          }
                          if (value!.length < 2) {
                            return 'اسم الجهاز يجب أن يكون حرفين على الأقل';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // نوع الجهاز
                      DropdownButtonFormField<String>(
                        value: _selectedDeviceType,
                        decoration: InputDecoration(
                          labelText: 'نوع الجهاز *',
                          prefixIcon: Icon(_getSelectedTypeIcon()),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: _deviceTypes.map((type) {
                          return DropdownMenuItem<String>(
                            value: type['value'],
                            child: Row(
                              children: [
                                Icon(type['icon'], size: 20),
                                const SizedBox(width: 8),
                                Text(type['label']),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedDeviceType = value!;
                          });
                        },
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'نوع الجهاز مطلوب';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // موديل الجهاز
                      TextFormField(
                        controller: _modelController,
                        decoration: InputDecoration(
                          labelText: 'موديل الجهاز *',
                          hintText: 'أدخل موديل الجهاز',
                          prefixIcon: const Icon(Icons.info),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'موديل الجهاز مطلوب';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // المستخدم المسؤول
                      DropdownButtonFormField<String>(
                        value: _selectedUserId,
                        decoration: InputDecoration(
                          labelText: 'المستخدم المسؤول *',
                          prefixIcon: const Icon(Icons.person),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: [
                          const DropdownMenuItem<String>(
                            value: null,
                            child: Text('اختر المستخدم المسؤول'),
                          ),
                          ...usersState.users.map((user) {
                            return DropdownMenuItem<String>(
                              value: user.id,
                              child: Row(
                                children: [
                                  CircleAvatar(
                                    radius: 12,
                                    backgroundColor: user.isAdmin 
                                        ? AppColors.primaryGold 
                                        : AppColors.info,
                                    child: Text(
                                      user.fullName?.substring(0, 1) ?? 'م',
                                      style: const TextStyle(
                                        color: AppColors.darkBackground,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          user.fullName ?? 'غير محدد',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                        if (user.department?.isNotEmpty == true)
                                          Text(
                                            user.department!,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: AppColors.secondaryText,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedUserId = value;
                          });
                        },
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'المستخدم المسؤول مطلوب';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // الموقع (اختياري)
                      DropdownButtonFormField<String>(
                        value: _selectedLocationId,
                        decoration: InputDecoration(
                          labelText: 'الموقع (اختياري)',
                          prefixIcon: const Icon(Icons.location_on),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem<String>(
                            value: null,
                            child: Text('اختر الموقع (اختياري)'),
                          ),
                          // سيتم تحميل المواقع لاحقاً
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedLocationId = value;
                          });
                        },
                      ),

                      const SizedBox(height: 16),

                      // الوصف
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 3,
                        decoration: InputDecoration(
                          labelText: 'الوصف (اختياري)',
                          hintText: 'أدخل وصف للجهاز',
                          prefixIcon: const Icon(Icons.description),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // أسفل النافذة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.surfaceBackground,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _createDevice,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryGold,
                      foregroundColor: AppColors.darkBackground,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: AppColors.darkBackground,
                            ),
                          )
                        : const Text('إنشاء الجهاز'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getSelectedTypeIcon() {
    final type = _deviceTypes.firstWhere((t) => t['value'] == _selectedDeviceType);
    return type['icon'];
  }

  Future<void> _createDevice() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await ref.read(devicesProvider.notifier).createDevice(
        deviceId: _deviceIdController.text.trim(),
        deviceName: _deviceNameController.text.trim(),
        deviceType: _selectedDeviceType,
        model: _modelController.text.trim(),
        userId: _selectedUserId!,
        locationId: _selectedLocationId,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      if (result['success']) {
        if (mounted) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إنشاء الجهاز بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['error'] ?? 'فشل في إنشاء الجهاز'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${error.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
