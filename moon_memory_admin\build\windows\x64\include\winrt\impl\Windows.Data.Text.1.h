// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Data_Text_1_H
#define WINRT_Windows_Data_Text_1_H
#include "winrt/impl/Windows.Data.Text.0.h"
WINRT_EXPORT namespace winrt::Windows::Data::Text
{
    struct __declspec(empty_bases) IAlternateWordForm :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IAlternateWordForm>
    {
        IAlternateWordForm(std::nullptr_t = nullptr) noexcept {}
        IAlternateWordForm(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectableWordSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectableWordSegment>
    {
        ISelectableWordSegment(std::nullptr_t = nullptr) noexcept {}
        ISelectableWordSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectableWordsSegmenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectableWordsSegmenter>
    {
        ISelectableWordsSegmenter(std::nullptr_t = nullptr) noexcept {}
        ISelectableWordsSegmenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISelectableWordsSegmenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISelectableWordsSegmenterFactory>
    {
        ISelectableWordsSegmenterFactory(std::nullptr_t = nullptr) noexcept {}
        ISelectableWordsSegmenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticTextQuery :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticTextQuery>
    {
        ISemanticTextQuery(std::nullptr_t = nullptr) noexcept {}
        ISemanticTextQuery(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ISemanticTextQueryFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISemanticTextQueryFactory>
    {
        ISemanticTextQueryFactory(std::nullptr_t = nullptr) noexcept {}
        ISemanticTextQueryFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextConversionGenerator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextConversionGenerator>
    {
        ITextConversionGenerator(std::nullptr_t = nullptr) noexcept {}
        ITextConversionGenerator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextConversionGeneratorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextConversionGeneratorFactory>
    {
        ITextConversionGeneratorFactory(std::nullptr_t = nullptr) noexcept {}
        ITextConversionGeneratorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextPhoneme :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextPhoneme>
    {
        ITextPhoneme(std::nullptr_t = nullptr) noexcept {}
        ITextPhoneme(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextPredictionGenerator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextPredictionGenerator>
    {
        ITextPredictionGenerator(std::nullptr_t = nullptr) noexcept {}
        ITextPredictionGenerator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextPredictionGenerator2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextPredictionGenerator2>
    {
        ITextPredictionGenerator2(std::nullptr_t = nullptr) noexcept {}
        ITextPredictionGenerator2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextPredictionGeneratorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextPredictionGeneratorFactory>
    {
        ITextPredictionGeneratorFactory(std::nullptr_t = nullptr) noexcept {}
        ITextPredictionGeneratorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextReverseConversionGenerator :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextReverseConversionGenerator>
    {
        ITextReverseConversionGenerator(std::nullptr_t = nullptr) noexcept {}
        ITextReverseConversionGenerator(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextReverseConversionGenerator2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextReverseConversionGenerator2>
    {
        ITextReverseConversionGenerator2(std::nullptr_t = nullptr) noexcept {}
        ITextReverseConversionGenerator2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) ITextReverseConversionGeneratorFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ITextReverseConversionGeneratorFactory>
    {
        ITextReverseConversionGeneratorFactory(std::nullptr_t = nullptr) noexcept {}
        ITextReverseConversionGeneratorFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IUnicodeCharactersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IUnicodeCharactersStatics>
    {
        IUnicodeCharactersStatics(std::nullptr_t = nullptr) noexcept {}
        IUnicodeCharactersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWordSegment :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWordSegment>
    {
        IWordSegment(std::nullptr_t = nullptr) noexcept {}
        IWordSegment(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWordsSegmenter :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWordsSegmenter>
    {
        IWordsSegmenter(std::nullptr_t = nullptr) noexcept {}
        IWordsSegmenter(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWordsSegmenterFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWordsSegmenterFactory>
    {
        IWordsSegmenterFactory(std::nullptr_t = nullptr) noexcept {}
        IWordsSegmenterFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
